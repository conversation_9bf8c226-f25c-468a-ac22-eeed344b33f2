I rknn-toolkit2 version: 2.3.2
--> Config model
done
--> Loading model

I Loading :   0%|                                                           | 0/134 [00:00<?, ?it/s]
I Loading : 100%|██████████████████████████████████████████████| 134/134 [00:00<00:00, 32116.38it/s]
done
--> Building model

I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   1%|▍                                               | 1/100 [00:00<00:00, 226.68it/s]
I OpFusing 0:   2%|▉                                               | 2/100 [00:00<00:00, 380.18it/s]
I OpFusing 0:   3%|█▍                                              | 3/100 [00:00<00:00, 264.32it/s]
I OpFusing 0:   4%|█▉                                              | 4/100 [00:00<00:00, 334.51it/s]
I OpFusing 0:   6%|██▉                                             | 6/100 [00:00<00:00, 420.18it/s]
I OpFusing 0:   7%|███▎                                            | 7/100 [00:00<00:00, 468.58it/s]
I OpFusing 0:   8%|███▊                                            | 8/100 [00:00<00:00, 513.52it/s]
I OpFusing 0:  10%|████▋                                          | 10/100 [00:00<00:00, 601.39it/s]
I OpFusing 0:  11%|█████▏                                         | 11/100 [00:00<00:00, 638.36it/s]
I OpFusing 0:  12%|█████▋                                         | 12/100 [00:00<00:00, 664.15it/s]
I OpFusing 0:  13%|██████                                         | 13/100 [00:00<00:00, 696.29it/s]
I OpFusing 0:  15%|███████                                        | 15/100 [00:00<00:00, 761.98it/s]
I OpFusing 0:  16%|███████▌                                       | 16/100 [00:00<00:00, 788.66it/s]
I OpFusing 0:  17%|███████▉                                       | 17/100 [00:00<00:00, 813.57it/s]
I OpFusing 0:  19%|████████▉                                      | 19/100 [00:00<00:00, 867.05it/s]
I OpFusing 0:  20%|█████████▍                                     | 20/100 [00:00<00:00, 889.06it/s]
I OpFusing 0:  22%|██████████▎                                    | 22/100 [00:00<00:00, 944.61it/s]
I OpFusing 0:  23%|██████████▊                                    | 23/100 [00:00<00:00, 964.83it/s]
I OpFusing 0:  25%|███████████▌                                  | 25/100 [00:00<00:00, 1007.80it/s]
I OpFusing 0:  26%|███████████▉                                  | 26/100 [00:00<00:00, 1024.42it/s]
I OpFusing 0:  27%|████████████▍                                 | 27/100 [00:00<00:00, 1039.12it/s]
I OpFusing 0:  29%|█████████████▎                                | 29/100 [00:00<00:00, 1073.73it/s]
I OpFusing 0:  30%|█████████████▊                                | 30/100 [00:00<00:00, 1084.57it/s]
I OpFusing 0:  32%|██████████████▋                               | 32/100 [00:00<00:00, 1113.86it/s]
I OpFusing 0:  33%|███████████████▏                              | 33/100 [00:00<00:00, 1122.16it/s]
I OpFusing 0:  37%|█████████████████                             | 37/100 [00:00<00:00, 1191.94it/s]
I OpFusing 0:  39%|█████████████████▉                            | 39/100 [00:00<00:00, 1220.87it/s]
I OpFusing 0:  41%|██████████████████▊                           | 41/100 [00:00<00:00, 1246.40it/s]
I OpFusing 0:  42%|███████████████████▎                          | 42/100 [00:00<00:00, 1254.61it/s]
I OpFusing 0:  44%|████████████████████▏                         | 44/100 [00:00<00:00, 1284.81it/s]
I OpFusing 0:  46%|█████████████████████▏                        | 46/100 [00:00<00:00, 1300.34it/s]
I OpFusing 0:  48%|██████████████████████                        | 48/100 [00:00<00:00, 1320.49it/s]
I OpFusing 0:  50%|███████████████████████                       | 50/100 [00:00<00:00, 1354.70it/s]
I OpFusing 0:  51%|███████████████████████▍                      | 51/100 [00:00<00:00, 1352.27it/s]
I OpFusing 0:  53%|████████████████████████▍                     | 53/100 [00:00<00:00, 1381.29it/s]
I OpFusing 0:  55%|█████████████████████████▎                    | 55/100 [00:00<00:00, 1402.59it/s]
I OpFusing 0:  57%|██████████████████████████▏                   | 57/100 [00:00<00:00, 1416.31it/s]
I OpFusing 0:  58%|██████████████████████████▋                   | 58/100 [00:00<00:00, 1419.39it/s]
I OpFusing 0:  60%|███████████████████████████▌                  | 60/100 [00:00<00:00, 1439.22it/s]
I OpFusing 0:  62%|████████████████████████████▌                 | 62/100 [00:00<00:00, 1464.70it/s]
I OpFusing 0:  64%|█████████████████████████████▍                | 64/100 [00:00<00:00, 1478.70it/s]
I OpFusing 0:  66%|██████████████████████████████▎               | 66/100 [00:00<00:00, 1488.41it/s]
I OpFusing 0:  67%|██████████████████████████████▊               | 67/100 [00:00<00:00, 1489.80it/s]
I OpFusing 0:  69%|███████████████████████████████▋              | 69/100 [00:00<00:00, 1505.96it/s]
I OpFusing 0:  71%|████████████████████████████████▋             | 71/100 [00:00<00:00, 1528.87it/s]
I OpFusing 0:  73%|█████████████████████████████████▌            | 73/100 [00:00<00:00, 1550.72it/s]
I OpFusing 0:  75%|██████████████████████████████████▌           | 75/100 [00:00<00:00, 1566.76it/s]
I OpFusing 0:  76%|██████████████████████████████████▉           | 76/100 [00:00<00:00, 1567.07it/s]
I OpFusing 0:  80%|████████████████████████████████████▊         | 80/100 [00:00<00:00, 1612.50it/s]
I OpFusing 0:  82%|█████████████████████████████████████▋        | 82/100 [00:00<00:00, 1633.20it/s]
I OpFusing 0:  84%|██████████████████████████████████████▋       | 84/100 [00:00<00:00, 1647.42it/s]
I OpFusing 0:  86%|███████████████████████████████████████▌      | 86/100 [00:00<00:00, 1665.79it/s]
I OpFusing 0:  90%|█████████████████████████████████████████▍    | 90/100 [00:00<00:00, 1706.59it/s]
I OpFusing 0:  92%|██████████████████████████████████████████▎   | 92/100 [00:00<00:00, 1724.83it/s]
I OpFusing 0:  95%|███████████████████████████████████████████▋  | 95/100 [00:00<00:00, 1755.12it/s]
I OpFusing 0:  96%|████████████████████████████████████████████▏ | 96/100 [00:00<00:00, 1753.75it/s]
I OpFusing 0: 100%|██████████████████████████████████████████████| 100/100 [00:00<00:00, 990.56it/s]
I OpFusing 1 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 1 :  82%|█████████████████████████████████████▋        | 82/100 [00:00<00:00, 630.06it/s]
I OpFusing 1 :  90%|█████████████████████████████████████████▍    | 90/100 [00:00<00:00, 676.51it/s]
I OpFusing 1 :  98%|█████████████████████████████████████████████ | 98/100 [00:00<00:00, 721.59it/s]
I OpFusing 1 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 560.09it/s]
I OpFusing 0 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 0 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 479.68it/s]
I OpFusing 1 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 1 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 424.17it/s]
I OpFusing 2 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 411.16it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 163.75it/s]
[1;33mW[0m [1;33mbuild: found outlier value, this may affect quantization accuracy
                        const name                        abs_mean    abs_std     outlier value
                        model.0.conv.weight               2.44        2.47        -17.494     
                        model.22.cv3.2.1.conv.weight      0.09        0.14        -10.215     
                        model.22.cv3.1.1.conv.weight      0.12        0.19        13.361, 13.317
                        model.22.cv3.0.1.conv.weight      0.18        0.20        -11.216     [0m





I GraphPreparing :   0%|                                                    | 0/161 [00:00<?, ?it/s]
I GraphPreparing : 100%|████████████████████████████████████████| 161/161 [00:00<00:00, 9241.08it/s]

I Quantizating :   0%|                                                      | 0/161 [00:00<?, ?it/s]
I Quantizating :   1%|▎                                             | 1/161 [00:00<00:42,  3.80it/s]
I Quantizating :   1%|▌                                             | 2/161 [00:00<00:44,  3.57it/s]
I Quantizating :   2%|▊                                             | 3/161 [00:00<00:33,  4.72it/s]
I Quantizating :   2%|█▏                                            | 4/161 [00:00<00:29,  5.31it/s]
I Quantizating :   4%|█▋                                            | 6/161 [00:01<00:20,  7.44it/s]
I Quantizating :   6%|██▌                                           | 9/161 [00:01<00:13, 11.36it/s]
I Quantizating :   7%|███                                          | 11/161 [00:01<00:11, 12.51it/s]
I Quantizating :   9%|████▏                                        | 15/161 [00:01<00:09, 14.78it/s]
I Quantizating :  11%|████▊                                        | 17/161 [00:01<00:10, 14.39it/s]
I Quantizating :  13%|█████▊                                       | 21/161 [00:01<00:07, 18.82it/s]
I Quantizating :  16%|██████▉                                      | 25/161 [00:01<00:05, 23.32it/s]
I Quantizating :  17%|███████▊                                     | 28/161 [00:02<00:05, 23.13it/s]
I Quantizating :  20%|█████████▏                                   | 33/161 [00:02<00:05, 25.55it/s]
I Quantizating :  22%|██████████                                   | 36/161 [00:02<00:04, 25.33it/s]
I Quantizating :  25%|███████████▍                                 | 41/161 [00:02<00:04, 29.34it/s]
I Quantizating :  29%|████████████▊                                | 46/161 [00:02<00:03, 34.13it/s]
I Quantizating :  32%|██████████████▌                              | 52/161 [00:02<00:03, 35.99it/s]
I Quantizating :  37%|████████████████▍                            | 59/161 [00:02<00:02, 41.49it/s]
I Quantizating :  44%|███████████████████▊                         | 71/161 [00:02<00:01, 59.39it/s]
I Quantizating :  48%|█████████████████████▊                       | 78/161 [00:03<00:01, 54.46it/s]
I Quantizating :  52%|███████████████████████▍                     | 84/161 [00:03<00:01, 53.08it/s]
I Quantizating :  56%|█████████████████████████▏                   | 90/161 [00:03<00:01, 42.69it/s]
I Quantizating :  59%|██████████████████████████▌                  | 95/161 [00:03<00:01, 41.14it/s]
I Quantizating :  62%|███████████████████████████▎                | 100/161 [00:03<00:01, 38.72it/s]
I Quantizating :  65%|████████████████████████████▋               | 105/161 [00:03<00:01, 38.90it/s]
I Quantizating :  68%|██████████████████████████████              | 110/161 [00:03<00:01, 39.94it/s]
I Quantizating :  73%|████████████████████████████████▏           | 118/161 [00:04<00:00, 45.97it/s]
I Quantizating :  78%|██████████████████████████████████▏         | 125/161 [00:04<00:00, 51.31it/s]
I Quantizating :  84%|█████████████████████████████████████▏      | 136/161 [00:04<00:00, 58.44it/s]
I Quantizating :  88%|██████████████████████████████████████▊     | 142/161 [00:04<00:00, 50.72it/s]
I Quantizating :  93%|████████████████████████████████████████▋   | 149/161 [00:04<00:00, 43.61it/s]
I Quantizating :  96%|██████████████████████████████████████████  | 154/161 [00:05<00:00, 26.00it/s]
I Quantizating :  98%|███████████████████████████████████████████▏| 158/161 [00:05<00:00, 19.18it/s]
I Quantizating : 100%|████████████████████████████████████████████| 161/161 [00:05<00:00, 28.78it/s]
[1;33mW[0m [1;33mbuild: The default input dtype of 'images' is changed from 'float32' to 'int8' in rknn model for performance!
                       Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '317' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '325' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '331' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '338' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '346' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '352' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '359' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '367' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '373' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
I rknn building ...
I rknn building done.
done
--> Export rknn model
done
